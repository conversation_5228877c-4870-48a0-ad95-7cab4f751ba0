@page
@model LeaderSurvey.Pages.CategoriesModel
@{
    ViewData["Title"] = "Categories";
}

<div class="d-flex justify-content-between align-items-center mb-4">
    <h2 class="page-title mb-0"><i class="bi bi-tags"></i> Categories Management</h2>
    <button type="button" class="cfa-btn cfa-btn-primary" onclick="showCategoryModal()">
        <i class="bi bi-plus-circle"></i> Create New Category
    </button>
</div>

@if (TempData["ErrorMessage"] != null)
{
    <div class="alert alert-danger alert-dismissible fade show" role="alert">
        <i class="bi bi-exclamation-triangle-fill me-2"></i> @TempData["ErrorMessage"]
        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
    </div>
}

@if (TempData["SuccessMessage"] != null)
{
    <div class="alert alert-success alert-dismissible fade show" role="alert">
        <i class="bi bi-check-circle-fill me-2"></i> @TempData["SuccessMessage"]
        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
    </div>
}

<div class="container">
    <div class="row">
        @foreach (var category in Model.Categories)
        {
            <div class="col-md-6 col-lg-4 mb-4">
                <div class="category-card" data-category-id="@category.Id">
                    <div class="category-header d-flex justify-content-between align-items-center">
                        <h5 class="category-name mb-0">@category.Name</h5>
                        <div class="category-actions">
                            <button class="btn btn-sm btn-outline-primary me-1" onclick="editCategory(@category.Id, '@category.Name', '@category.Description')">
                                <i class="bi bi-pencil"></i>
                            </button>
                            <button class="btn btn-sm btn-outline-danger" onclick="deleteCategory(@category.Id, '@category.Name')">
                                <i class="bi bi-trash"></i>
                            </button>
                        </div>
                    </div>
                    <div class="category-description mt-2">
                        @category.Description
                    </div>
                    <div class="category-stats mt-3">
                        <div class="stat-item">
                            <span class="stat-label">Questions:</span>
                            <span class="stat-value question-count" id="<EMAIL>">@(Model.CategoryQuestionCounts.ContainsKey(category.Id) ? Model.CategoryQuestionCounts[category.Id] : 0)</span>
                        </div>
                    </div>
                </div>
            </div>
        }
    </div>
</div>

<!-- Category Modal -->
<div class="modal fade" id="categoryModal" tabindex="-1" aria-labelledby="categoryModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="categoryModalLabel">Create New Category</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <form id="categoryForm">
                    <input type="hidden" id="categoryId" value="0">
                    <div class="mb-3">
                        <label for="categoryName" class="form-label">Category Name</label>
                        <input type="text" class="form-control" id="categoryName" required>
                    </div>
                    <div class="mb-3">
                        <label for="categoryDescription" class="form-label">Description</label>
                        <textarea class="form-control" id="categoryDescription" rows="3"></textarea>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <button type="button" class="btn btn-primary" onclick="saveCategory()">Save</button>
            </div>
        </div>
    </div>
</div>

<!-- Delete Confirmation Modal -->
<div class="modal fade" id="deleteCategoryModal" tabindex="-1" aria-labelledby="deleteCategoryModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="deleteCategoryModalLabel">Confirm Delete</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <p>Are you sure you want to delete the category "<span id="deleteCategoryName"></span>"?</p>
                <p class="text-danger">This action cannot be undone.</p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <button type="button" class="btn btn-danger" id="confirmDeleteBtn">Delete</button>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    <script src="~/js/categories.js"></script>
}

@section Styles {
    <link rel="stylesheet" href="~/css/categories.css" />
}
