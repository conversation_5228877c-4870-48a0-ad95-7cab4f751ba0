﻿@page
@model LeaderSurvey.Pages.LeadersModel
@{
    ViewData["Title"] = "Leaders";
}
@Html.AntiForgeryToken()

<div class="d-flex justify-content-between align-items-center mb-3">
    <h2 class="page-title mb-0"><i class="bi bi-people-fill"></i> Team Management</h2>
</div>

<ul class="nav nav-tabs mb-4" id="managementTabs">
    <li class="nav-item">
        <a href="?tab=leaders" class="nav-link @(Model.ActiveTab == "leaders" ? "active" : "")" id="leaders-tab">
            <i class="bi bi-people-fill"></i> Leaders
        </a>
    </li>
    <li class="nav-item">
        <a href="?tab=categories" class="nav-link @(Model.ActiveTab == "categories" ? "active" : "")" id="categories-tab">
            <i class="bi bi-tags"></i> Categories
        </a>
    </li>
</ul>

<script>
    function showLeadersTab() {
        document.getElementById('leaders-content').style.display = 'block';
        document.getElementById('categories-content').style.display = 'none';
        document.getElementById('leaders-tab').classList.add('active');
        document.getElementById('categories-tab').classList.remove('active');
    }

    function showCategoriesTab() {
        document.getElementById('categories-content').style.display = 'block';
        document.getElementById('leaders-content').style.display = 'none';
        document.getElementById('categories-tab').classList.add('active');
        document.getElementById('leaders-tab').classList.remove('active');
    }

    // Execute the appropriate function based on the active tab
    document.addEventListener('DOMContentLoaded', function() {
        const activeTab = '@Model.ActiveTab';
        console.log('Initializing with active tab:', activeTab);

        // Force the correct tab to be visible
        if (activeTab === 'leaders') {
            showLeadersTab();
        } else if (activeTab === 'categories') {
            showCategoriesTab();
        } else {
            // Default to leaders tab if no tab is specified
            showLeadersTab();
        }
    });
</script>

<div class="tab-content" id="managementTabsContent" style="display: block !important;">
    <!-- Leaders Tab Content -->
    <div class="tab-pane @(Model.ActiveTab == "leaders" ? "show active" : "")" id="leaders-content" role="tabpanel" aria-labelledby="leaders-tab" style="display: @(Model.ActiveTab == "leaders" ? "block" : "none") !important;">
        <div class="container">
            <div class="filters-section">
                <div class="d-flex gap-3 align-items-center">
                    <div class="filter-group">
                        <select id="areaFilter" class="cfa-select" aria-label="Filter by area">
                            <option value="">All Areas</option>
                            <option value="Drive">Drive</option>
                            <option value="Front">Front</option>
                            <option value="Kitchen">Kitchen</option>
                            <option value="Hospitality">Hospitality</option>
                        </select>
                    </div>
                    <div class="filter-group">
                        <button id="sortToggle" class="cfa-btn" onclick="toggleSort()">
                            <i class="bi bi-sort-alpha-down"></i> Sort A-Z
                        </button>
                    </div>
                    <div class="ms-auto">
                        <button class="cfa-btn" onclick="showAddLeaderModal()">
                            <i class="bi bi-person-plus-fill"></i> Add New Leader
                        </button>
                    </div>
                </div>
            </div>

            <div class="table-responsive">
                <table id="leadersTable" class="table table-cfa">
                    <thead>
                        <tr>
                            <th>Name</th>
                            <th>Area</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        @if (Model.Leaders != null && Model.Leaders.Any())
                        {
                            @foreach (var leader in Model.Leaders)
                            {
                                <tr data-id="@leader.Id">
                                    <td>@leader.Name</td>
                                    <td>
                                        <span class="badge area-badge <EMAIL>()">@leader.Area</span>
                                    </td>
                                    <td>
                                        <div class="action-buttons">
                                            <button class="btn btn-sm btn-outline-primary me-2" onclick="editLeader(@leader.Id)">
                                                <i class="bi bi-pencil"></i>
                                            </button>
                                            <button class="btn btn-sm btn-outline-danger" onclick="deleteLeader(@leader.Id, '@leader.Name')">
                                                <i class="bi bi-trash"></i>
                                            </button>
                                        </div>
                                    </td>
                                </tr>
                            }
                        }
                    </tbody>
                </table>
            </div>
        </div>
    </div>

    <!-- Categories Tab Content -->
    <div class="tab-pane @(Model.ActiveTab == "categories" ? "show active" : "")" id="categories-content" role="tabpanel" aria-labelledby="categories-tab" style="display: @(Model.ActiveTab == "categories" ? "block" : "none") !important;">
        <div class="container">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <div></div> <!-- Empty div for spacing -->
                <button type="button" class="cfa-btn cfa-btn-primary" onclick="showCategoryModal()">
                    <i class="bi bi-plus-circle"></i> Create New Category
                </button>
            </div>

            <div class="row">
                @foreach (var category in Model.Categories)
                {
                    <div class="col-md-6 col-lg-4 mb-4">
                        <div class="category-card" data-category-id="@category.Id">
                            <div class="category-header d-flex justify-content-between align-items-center">
                                <h5 class="category-name mb-0">@category.Name</h5>
                                <div class="category-actions">
                                    <button class="btn btn-sm btn-outline-primary me-1" onclick="editCategory(@category.Id, '@category.Name', '@category.Description')">
                                        <i class="bi bi-pencil"></i>
                                    </button>
                                    <button class="btn btn-sm btn-outline-danger" onclick="deleteCategory(@category.Id, '@category.Name')">
                                        <i class="bi bi-trash"></i>
                                    </button>
                                </div>
                            </div>
                            <div class="category-description mt-2">
                                @category.Description
                            </div>
                            <div class="category-stats mt-3">
                                <div class="stat-item">
                                    <span class="stat-label">Questions:</span>
                                    <span class="stat-value question-count" id="<EMAIL>">@(Model.CategoryQuestionCounts.ContainsKey(category.Id) ? Model.CategoryQuestionCounts[category.Id] : 0)</span>
                                </div>
                            </div>
                        </div>
                    </div>
                }
            </div>
        </div>
    </div>
</div>

<!-- Category Modal -->
<div class="modal fade" id="categoryModal" tabindex="-1" aria-labelledby="categoryModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="categoryModalLabel">Create New Category</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <form id="categoryForm">
                    @Html.AntiForgeryToken()
                    <input type="hidden" id="categoryId" value="0">
                    <div class="mb-3">
                        <label for="categoryName" class="form-label">Category Name</label>
                        <input type="text" class="form-control" id="categoryName" required>
                    </div>
                    <div class="mb-3">
                        <label for="categoryDescription" class="form-label">Description</label>
                        <textarea class="form-control" id="categoryDescription" rows="3"></textarea>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <button type="button" class="btn btn-primary" onclick="saveCategory()">Save</button>
            </div>
        </div>
    </div>
</div>

<!-- Delete Confirmation Modal for Categories -->
<div class="modal fade" id="deleteCategoryModal" tabindex="-1" aria-labelledby="deleteCategoryModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="deleteCategoryModalLabel">Confirm Delete</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <p>Are you sure you want to delete the category "<span id="deleteCategoryName"></span>"?</p>
                <p class="text-danger">This action cannot be undone.</p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <button type="button" class="btn btn-danger" id="confirmDeleteBtn">Delete</button>
            </div>
        </div>
    </div>
</div>

<!-- Add/Edit Leader Modal -->
<div class="modal fade" id="leaderModal" tabindex="-1" aria-labelledby="leaderModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="leaderModalLabel">Add New Leader</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <form method="post" id="leaderForm" class="needs-validation" novalidate>
                    @Html.AntiForgeryToken()
                    <input type="hidden" id="leaderId" name="Id" />
                    <div class="mb-3">
                        <label for="Name" class="form-label">Name</label>
                        <input type="text" class="form-control" id="Name" name="NewLeader.Name" required>
                        <div class="invalid-feedback">Please enter the leader's name.</div>
                    </div>
                    <div class="mb-3">
                        <label for="Area" class="form-label">Area</label>
                        <select class="form-control" id="Area" name="NewLeader.Area" required>
                            <option value="">Select Area</option>
                            <option value="Front">Front</option>
                            <option value="Drive">Drive</option>
                            <option value="Kitchen">Kitchen</option>
                            <option value="Hospitality">Hospitality</option>
                        </select>
                        <div class="invalid-feedback">Please select an area.</div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <button type="submit" form="leaderForm" class="btn btn-primary">Save Leader</button>
            </div>
        </div>
    </div>
</div>

@section Styles {
    <link rel="stylesheet" href="~/css/categories.css" />
    <style>
        /* Fix for tab content visibility */
        .tab-content {
            display: block !important;
        }
        .tab-pane.active {
            display: block !important;
        }
        .tab-pane {
            transition: opacity 0.15s linear;
        }
        .tab-pane.show {
            opacity: 1;
        }

        /* Custom tab styling */
        #managementTabs {
            border-bottom: 1px solid #dee2e6;
        }

        #managementTabs .nav-link {
            color: #495057;
            background-color: transparent;
            border: 1px solid transparent;
            border-top-left-radius: 0.25rem;
            border-top-right-radius: 0.25rem;
            padding: 0.5rem 1rem;
            margin-bottom: -1px;
            text-decoration: none;
        }

        #managementTabs .nav-link:hover {
            border-color: #e9ecef #e9ecef #dee2e6;
        }

        #managementTabs .nav-link.active {
            color: #495057;
            background-color: #fff;
            border-color: #dee2e6 #dee2e6 #fff;
        }
    </style>
}

@section Scripts {
    <partial name="_ValidationScriptsPartial" />
    <script src="~/js/categories.js"></script>
    <script>
        let leaderModal;
        let isAscending = true;

        document.addEventListener('DOMContentLoaded', function() {
            leaderModal = new bootstrap.Modal(document.getElementById('leaderModal'));
            setupFilters();

            // No need for tab initialization since we're using regular links with page reloads
            console.log('Active tab:', '@Model.ActiveTab');

            // Make sure the correct content is visible on page load
            const activeTab = '@Model.ActiveTab';
            if (activeTab === 'leaders') {
                showLeadersTab();
            } else if (activeTab === 'categories') {
                showCategoriesTab();
            }

            const form = document.getElementById('leaderForm');
            form.addEventListener('submit', async function(e) {
                e.preventDefault();

                if (!form.checkValidity()) {
                    form.classList.add('was-validated');
                    return;
                }

                try {
                    const leaderId = document.getElementById('leaderId').value;
                    const name = document.getElementById('Name').value.trim();
                    const area = document.getElementById('Area').value.trim();

                    if (!name || !area) {
                        showNotification('Name and Area are required', 'error');
                        return;
                    }

                    const token = document.querySelector('input[name="__RequestVerificationToken"]').value;

                    // Determine if this is an edit or a new leader
                    const isEdit = leaderId !== '';
                    const url = isEdit ? '?handler=Update' : '?handler=Post';

                    const response = await fetch(url, {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                            'RequestVerificationToken': token
                        },
                        body: JSON.stringify({
                            leader: {
                                id: isEdit ? parseInt(leaderId) : 0,
                                name: name,
                                area: area
                            }
                        })
                    });

                    const responseData = await response.json();
                    console.log('Response data:', responseData);

                    if (responseData.success) {
                        if (isEdit) {
                            // Update existing row
                            const row = document.querySelector(`tr[data-id="${leaderId}"]`);
                            row.innerHTML = `
                                <td>${name}</td>
                                <td><span class="badge area-badge area-${area.toLowerCase()}">${area}</span></td>
                                <td>
                                    <div class="action-buttons">
                                        <button class="btn btn-sm btn-outline-primary me-2" onclick="editLeader(${leaderId})">
                                            <i class="bi bi-pencil"></i>
                                        </button>
                                        <button class="btn btn-sm btn-outline-danger" onclick="deleteLeader(${leaderId}, '${name}')">
                                            <i class="bi bi-trash"></i>
                                        </button>
                                    </div>
                                </td>
                            `;
                        } else {
                            // Add new row
                            const tbody = document.querySelector('#leadersTable tbody');
                            const newRow = document.createElement('tr');
                            newRow.dataset.id = responseData.id;
                            newRow.innerHTML = `
                                <td>${responseData.name}</td>
                                <td><span class="badge area-badge area-${responseData.area.toLowerCase()}">${responseData.area}</span></td>
                                <td>
                                    <div class="action-buttons">
                                        <button class="btn btn-sm btn-outline-primary me-2" onclick="editLeader(${responseData.id})">
                                            <i class="bi bi-pencil"></i>
                                        </button>
                                        <button class="btn btn-sm btn-outline-danger" onclick="deleteLeader(${responseData.id}, '${responseData.name}')">
                                            <i class="bi bi-trash"></i>
                                        </button>
                                    </div>
                                </td>
                            `;
                            tbody.appendChild(newRow);
                        }

                        // Reset form and close modal
                        form.reset();
                        form.classList.remove('was-validated');
                        leaderModal.hide();

                        showNotification(`Leader ${isEdit ? 'updated' : 'added'} successfully`, 'success');
                    } else {
                        showNotification(responseData.message || `Failed to ${isEdit ? 'update' : 'save'} leader`, 'error');
                    }
                } catch (error) {
                    console.error('Error:', error);
                    showNotification('An error occurred while processing the leader', 'error');
                }
            });
        });

        function showAddLeaderModal() {
            document.getElementById('leaderModalLabel').textContent = 'Add New Leader';
            document.getElementById('leaderForm').reset();
            document.getElementById('leaderId').value = '';
            leaderModal.show();
        }

        function editLeader(id) {
            const row = document.querySelector(`tr[data-id="${id}"]`);
            const name = row.cells[0].textContent;
            const area = row.cells[1].querySelector('.badge').textContent;

            document.getElementById('leaderModalLabel').textContent = 'Edit Leader';
            document.getElementById('leaderId').value = id;
            document.getElementById('Name').value = name;
            document.getElementById('Area').value = area;

            leaderModal.show();
        }

        function deleteLeader(id, name) {
            if (confirm(`Are you sure you want to delete ${name}?`)) {
                const token = document.querySelector('input[name="__RequestVerificationToken"]').value;

                fetch(`?handler=Delete`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'RequestVerificationToken': token,
                        'X-Requested-With': 'XMLHttpRequest'
                    },
                    body: JSON.stringify({ id: id })
                })
                .then(response => {
                    if (!response.ok) {
                        throw new Error(`HTTP error! status: ${response.status}`);
                    }
                    return response.json();
                })
                .then(data => {
                    if (data.success) {
                        document.querySelector(`tr[data-id="${id}"]`).remove();
                        showNotification('Leader deleted successfully', 'success');
                    } else {
                        showNotification(data.message || 'Error deleting leader', 'error');
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    showNotification('Error deleting leader', 'error');
                });
            }
        }

        function setupFilters() {
            const areaFilter = document.getElementById('areaFilter');
            const rows = document.querySelectorAll('#leadersTable tbody tr');

            areaFilter.addEventListener('change', function() {
                filterTable();
            });
        }

        function toggleSort() {
            isAscending = !isAscending;
            const button = document.getElementById('sortToggle');
            button.innerHTML = isAscending ?
                '<i class="bi bi-sort-alpha-down"></i> Sort A-Z' :
                '<i class="bi bi-sort-alpha-up"></i> Sort Z-A';

            sortTable();
        }

        function sortTable() {
            const tbody = document.querySelector('#leadersTable tbody');
            const rows = Array.from(tbody.querySelectorAll('tr'));

            rows.sort((a, b) => {
                const nameA = a.querySelector('td').textContent.trim().toLowerCase();
                const nameB = b.querySelector('td').textContent.trim().toLowerCase();
                return isAscending ?
                    nameA.localeCompare(nameB) :
                    nameB.localeCompare(nameA);
            });

            // Clear the table body
            tbody.innerHTML = '';

            // Add sorted rows back
            rows.forEach(row => tbody.appendChild(row));

            // Apply current area filter after sorting
            filterTable();
        }

        function filterTable() {
            const selectedArea = document.getElementById('areaFilter').value.toLowerCase();
            const rows = document.querySelectorAll('#leadersTable tbody tr');

            rows.forEach(row => {
                const areaCell = row.querySelector('.area-badge');
                const rowArea = areaCell.textContent.toLowerCase();

                if (!selectedArea || rowArea === selectedArea) {
                    row.style.display = '';
                } else {
                    row.style.display = 'none';
                }
            });
        }

        // Initialize filters and sorting when the page loads
        document.addEventListener('DOMContentLoaded', function() {
            setupFilters();
        });
    </script>
}
