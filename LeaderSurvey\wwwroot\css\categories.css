.category-card {
    background-color: #fff;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    padding: 16px;
    height: 100%;
    transition: transform 0.2s, box-shadow 0.2s;
}

.category-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
}

.category-header {
    border-bottom: 1px solid #e9ecef;
    padding-bottom: 10px;
    margin-bottom: 10px;
}

.category-name {
    font-weight: 600;
    color: #333;
}

.category-description {
    color: #6c757d;
    font-size: 0.9rem;
    min-height: 40px;
}

.category-stats {
    margin-top: 15px;
    font-size: 0.9rem;
}

.stat-item {
    display: flex;
    align-items: center;
    margin-bottom: 5px;
}

.stat-label {
    color: #6c757d;
    margin-right: 8px;
}

.stat-value {
    font-weight: 600;
    color: #333;
}

.question-count {
    background-color: #e9ecef;
    border-radius: 12px;
    padding: 2px 8px;
    font-size: 0.8rem;
}

/* Modal styles */
.modal-header {
    background-color: #f8f9fa;
    border-bottom: 1px solid #e9ecef;
}

.modal-footer {
    background-color: #f8f9fa;
    border-top: 1px solid #e9ecef;
}

/* Form styles */
.form-label {
    font-weight: 500;
    color: #495057;
}

/* Button styles */
.cfa-btn {
    font-weight: 500;
    border-radius: 4px;
    padding: 8px 16px;
    transition: all 0.2s;
}

.cfa-btn-primary {
    background-color: #e51636;
    border-color: #e51636;
    color: white;
}

.cfa-btn-primary:hover {
    background-color: #c41230;
    border-color: #c41230;
}

/* Notification container */
#notification-container {
    position: fixed;
    top: 20px;
    right: 20px;
    z-index: 9999;
}

/* Alert styles */
.alert {
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    border-radius: 4px;
    margin-bottom: 10px;
}

.alert-success {
    background-color: #d4edda;
    border-color: #c3e6cb;
    color: #155724;
}

.alert-danger {
    background-color: #f8d7da;
    border-color: #f5c6cb;
    color: #721c24;
}

.alert-info {
    background-color: #d1ecf1;
    border-color: #bee5eb;
    color: #0c5460;
}
