﻿@page
@model LeaderSurvey.Pages.QuestionsModel
@{
    ViewData["Title"] = "Questions";
}

<div class="container">
    <h2 class="page-title">Survey Questions</h2>
    
    <div class="card mb-4">
        <div class="card-header d-flex justify-content-between align-items-center">
            <h5 class="mb-0">Questions <span id="question-counter" class="badge bg-cfa-red">0/10</span></h5>
            <button id="add-question-btn" class="cfa-btn cfa-btn-sm" onclick="addQuestion()">
                <i class="bi bi-plus-circle"></i> Add Question
            </button>
        </div>
        <div class="card-body">
            <div id="questions-container" class="questions-sortable">
                <!-- Questions will be added here dynamically -->
            </div>
        </div>
    </div>
</div>

@section Scripts {
    <script src="https://cdn.jsdelivr.net/npm/sortablejs@1.15.0/Sortable.min.js"></script>
    <script src="~/js/questions.js"></script>
}
