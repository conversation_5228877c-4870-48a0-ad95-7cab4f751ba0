@page "/survey/{surveyId:int}"
@model LeaderSurvey.Pages.PublicTakeSurveyModel
@{
    Layout = "_PublicLayout";
    ViewData["Title"] = "Complete Your Survey";
}

@if (TempData["StatusMessage"] != null)
{
    <div class="alert alert-success alert-dismissible fade show" role="alert">
        <i class="bi bi-check-circle-fill me-2"></i> @TempData["StatusMessage"]
        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
    </div>
}

@if (!string.IsNullOrEmpty(Model.Survey?.Name))
{
    <div class="text-center mb-4">
        <h2 class="h3 text-primary">
            <i class="bi bi-check2-square"></i> @Model.Survey.Name
        </h2>
        <p class="text-muted">Please complete all questions below</p>
    </div>
}

<div class="card shadow-sm mb-4">
    <div class="card-header bg-light">
        <i class="bi bi-clipboard-check me-1"></i> <strong>Survey Form</strong>
    </div>
    <div class="card-body">
        <form method="post" id="surveyForm">
            <input type="hidden" name="SurveyId" value="@Model.SurveyId" />
            @Html.AntiForgeryToken()

            @if (!ViewData.ModelState.IsValid)
            {
                <div class="alert alert-danger mb-4">
                    <i class="bi bi-exclamation-triangle-fill me-2"></i> <strong>Please correct the following errors:</strong>
                    <div asp-validation-summary="All" class="mt-2 mb-0"></div>
                </div>
            }

            <div class="alert alert-primary mb-4">
                <h5><i class="bi bi-info-circle-fill me-2"></i>About This Survey</h5>
                <p>This survey is designed to collect feedback about a leader in the @Model.Survey?.Area area.</p>
                <p><strong>Instructions:</strong> @(Model.Survey?.EvaluatorLeader?.Name ?? "You") will be taking this survey to evaluate @(Model.Survey?.Leader?.Name ?? "the leader"). Please answer the questions based on your observations. Your responses will be reviewed by Sean and used for leadership development.</p>
                <p class="mb-0"><strong>Note:</strong> The optional notes section at the bottom is for providing additional context that may be helpful for leadership review.</p>
            </div>

            <!-- Leader Selection -->
            @if (Model.Survey?.LeaderId.HasValue == true && Model.Survey.Leader != null)
            {
                <!-- Leader is pre-assigned, show as read-only -->
                <div class="mb-4">
                    <label class="form-label fw-bold">
                        <i class="bi bi-person-fill me-2"></i>Leader Being Evaluated
                    </label>
                    <div class="form-control-plaintext bg-light p-3 rounded border">
                        <i class="bi bi-check-circle-fill text-success me-2"></i>
                        <strong>@Model.Survey.Leader.Name</strong>
                        <small class="text-muted ms-2">(@Model.Survey.Leader.Area)</small>
                    </div>
                    <input type="hidden" name="SelectedLeaderId" value="@Model.Survey.LeaderId" />
                </div>
            }
            else
            {
                <!-- Allow leader selection -->
                <div class="mb-4">
                    <label for="SelectedLeaderId" class="form-label fw-bold">
                        <i class="bi bi-person-fill me-2"></i>Select Leader to Evaluate <span class="text-danger">*</span>
                    </label>
                    <select asp-for="SelectedLeaderId" asp-items="Model.LeaderList" class="form-select">
                        <option value="">-- Select a Leader --</option>
                    </select>
                    <span asp-validation-for="SelectedLeaderId" class="text-danger"></span>
                </div>
            }

            @if (Model.Questions != null && Model.Questions.Any())
            {
                <h5 class="mb-3"><i class="bi bi-list-check me-2"></i>Survey Questions</h5>
                <div class="mb-4" id="questionsContainer">
                    @foreach (var question in Model.Questions)
                    {
                        <div class="card mb-3 border-light shadow-sm question-card" data-question-id="@question.Id" data-question-type="@(question.QuestionType?.ToLower() ?? "text")">
                            <div class="card-header">
                                <div class="d-flex justify-content-between align-items-start flex-wrap">
                                    <strong class="me-2"><i class="bi bi-question-circle me-2"></i>@question.Text</strong>
                                    <span class="question-type-badge">
                                        @switch (question.QuestionType?.ToLower() ?? "text")
                                        {
                                            case "yesno":
                                                <span class="badge bg-success"><i class="bi bi-check-circle me-1"></i>Yes/No Question</span>
                                                break;
                                            case "score":
                                                <span class="badge bg-primary"><i class="bi bi-star me-1"></i>Score (0-10)</span>
                                                break;
                                            case "text":
                                            default:
                                                <span class="badge bg-secondary"><i class="bi bi-chat-text me-1"></i>Text Response</span>
                                                break;
                                        }
                                    </span>
                                </div>
                            </div>
                            <div class="card-body">
                                @switch (question.QuestionType?.ToLower() ?? "text")
                                {
                                    case "yesno":
                                        <div class="row">
                                            <div class="col-6">
                                                <div class="form-check">
                                                    <input class="form-check-input" type="radio" name="Answers[@question.Id]" id="<EMAIL>" value="Yes" />
                                                    <label class="form-check-label fw-bold text-success" for="<EMAIL>">
                                                        <i class="bi bi-check-circle me-1"></i> Yes
                                                    </label>
                                                </div>
                                            </div>
                                            <div class="col-6">
                                                <div class="form-check">
                                                    <input class="form-check-input" type="radio" name="Answers[@question.Id]" id="<EMAIL>" value="No" />
                                                    <label class="form-check-label fw-bold text-danger" for="<EMAIL>">
                                                        <i class="bi bi-x-circle me-1"></i> No
                                                    </label>
                                                </div>
                                            </div>
                                        </div>
                                        break;

                                    case "score":
                                        <div class="score-question">
                                            <div class="d-flex justify-content-between align-items-center mb-3">
                                                <span class="fw-bold">Rate from 0 (Poor) to 10 (Excellent):</span>
                                                <span id="<EMAIL>" class="badge bg-secondary px-3 py-2">Not Selected</span>
                                            </div>
                                            <div class="row g-2">
                                                @for (int i = 0; i <= 10; i++)
                                                {
                                                    <div class="col-6 col-sm-4 col-md-3 col-lg-2">
                                                        <div class="form-check">
                                                            <input class="form-check-input" type="radio" name="Answers[@question.Id]" id="score_@(question.Id)_@i" value="@i" onchange="updateScoreValue(@question.Id, @i)" />
                                                            <label class="form-check-label d-block text-center py-2 border rounded score-label" for="score_@(question.Id)_@i">
                                                                <strong>@i</strong>
                                                            </label>
                                                        </div>
                                                    </div>
                                                }
                                            </div>
                                        </div>
                                        break;

                                    case "text":
                                    default:
                                        <textarea name="Answers[@question.Id]" class="form-control" rows="4" placeholder="Enter your response here..."></textarea>
                                        break;
                                }

                                <div id="<EMAIL>" class="text-danger mt-2" style="display: none;"></div>
                            </div>
                        </div>
                    }
                </div>

                <!-- Additional Notes Section -->
                <div class="card mb-4 border-light shadow-sm">
                    <div class="card-header">
                        <strong><i class="bi bi-chat-text me-2"></i>Additional Notes (Optional)</strong>
                    </div>
                    <div class="card-body">
                        <textarea asp-for="AdditionalNotes" class="form-control" rows="4" placeholder="Any additional comments or observations about this leader's performance..."></textarea>
                        <div class="form-text">
                            <i class="bi bi-info-circle me-1"></i>
                            This section is optional. Use it to provide any additional context that might be helpful for leadership review.
                        </div>
                    </div>
                </div>

                <div class="d-grid gap-2">
                    <button type="submit" class="cfa-btn cfa-btn-primary" id="submitSurvey">
                        <i class="bi bi-send"></i> Submit Survey
                    </button>
                </div>
                <div class="mt-3 w-100" id="formErrorMessage"></div>
            }
            else
            {
                <div class="alert alert-warning">
                    <i class="bi bi-exclamation-triangle me-2"></i>
                    <strong>No questions found for this survey.</strong>
                    Please contact the administrator.
                </div>
            }
        </form>
    </div>
</div>

@section Scripts {
    <style>
        .score-label {
            cursor: pointer;
            transition: all 0.2s ease;
        }

        .score-label:hover {
            background-color: #f8f9fa;
            border-color: #007bff !important;
        }

        .form-check-input:checked + .score-label {
            background-color: #007bff;
            color: white;
            border-color: #007bff !important;
        }

        .question-card {
            transition: border-color 0.3s ease;
        }

        .question-card.border-danger {
            border-color: #dc3545 !important;
        }

        .form-disabled {
            opacity: 0.7;
            pointer-events: none;
        }
    </style>

    <script src="~/js/takeSurvey.js"></script>

    <script>
        // Initialize the form when the document is ready
        document.addEventListener('DOMContentLoaded', function() {
            console.log('DOMContentLoaded event fired');
            if (typeof initializeSurveyForm === 'function') {
                initializeSurveyForm();
            }
        });
    </script>
}
