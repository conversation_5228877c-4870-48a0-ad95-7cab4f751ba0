<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>@ViewData["Title"] - Leader Survey</title>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.2.3/dist/css/bootstrap.min.css" integrity="sha384-rbsA2VBKQhggwzxH7pPCaAqO46MgnOM80zW1RWuH61DGLwZJEdK2Kadq2F9CUG65" crossorigin="anonymous">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.7.2/font/bootstrap-icons.css">
    <link rel="stylesheet" href="~/css/site.css" />
    <link rel="icon" type="image/x-icon" href="~/favicon.ico">
    @await RenderSectionAsync("Styles", required: false)
</head>
<body>
    <div id="notification-container"></div>
    
    <!-- Minimal header for public survey pages -->
    <header class="bg-light border-bottom mb-4">
        <div class="container">
            <div class="py-3 text-center">
                <h1 class="h4 mb-0">
                    <i class="bi bi-clipboard-data text-primary"></i> 
                    @ViewData["Title"]
                </h1>
            </div>
        </div>
    </header>

    <div class="container">
        <main role="main" class="pb-3">
            @RenderBody()
        </main>
    </div>

    <footer class="border-top footer text-muted mt-5">
        <div class="container">
            <div class="row">
                <div class="col-md-12 text-center">
                    © 2025 - Leader Survey - All Rights Reserved
                </div>
            </div>
        </div>
    </footer>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.2.3/dist/js/bootstrap.bundle.min.js" integrity="sha384-kenU1KFdBIe4zVF0s0G1M5b4hcpxyD9F7jL+jjXkk+Q2h455rYXK/7HAuoJl+0I4" crossorigin="anonymous"></script>
    <script src="~/js/site.js" asp-append-version="true"></script>

    @await RenderSectionAsync("Scripts", required: false)
</body>
</html>
