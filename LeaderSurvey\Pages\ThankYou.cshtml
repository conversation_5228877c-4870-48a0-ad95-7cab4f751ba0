@page
@model LeaderSurvey.Pages.ThankYouModel
@{
    Layout = "_PublicLayout";
    ViewData["Title"] = "Thank You";
}

<div class="text-center">
    <div class="mb-4">
        <i class="bi bi-check-circle-fill text-success" style="font-size: 4rem;"></i>
    </div>
    
    <h1 class="h2 text-success mb-3">Thank You!</h1>
    
    <div class="card shadow-sm mx-auto" style="max-width: 600px;">
        <div class="card-body p-4">
            @if (!string.IsNullOrEmpty(Model.Message))
            {
                <p class="lead mb-3">@Model.Message</p>
            }
            else
            {
                <p class="lead mb-3">Thank you for completing the survey!</p>
            }
            
            <p class="text-muted mb-4">
                Your feedback has been successfully submitted and will be reviewed by the leadership team. 
                Your responses are valuable for leadership development and team improvement.
            </p>
            
            <div class="alert alert-info">
                <i class="bi bi-info-circle me-2"></i>
                <strong>What happens next?</strong><br>
                Your responses will be reviewed by <PERSON> and used for leadership development purposes. 
                Thank you for taking the time to provide thoughtful feedback.
            </div>
        </div>
    </div>
    
    <div class="mt-4">
        <p class="text-muted">You may now close this window.</p>
    </div>
</div>
