@page
@model LeaderSurvey.Pages.NewSurveyModel
@{
    ViewData["Title"] = "Create New Survey";
}

<div class="survey-editor">
    <div class="survey-editor-header">
        <h2 class="page-title"><i class="bi bi-plus-circle"></i> Create New Survey</h2>
    </div>

    <div class="survey-editor-content">
        <form id="surveyForm" method="post" class="needs-validation" novalidate>
            @Html.AntiForgeryToken()
            <div class="card mb-4">
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label asp-for="Survey.Name" class="form-label">Survey Name</label>
                                <input type="text" name="Survey.Name" id="Survey_Name" class="form-control" required />
                                <span asp-validation-for="Survey.Name" class="text-danger"></span>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label asp-for="Survey.Area" class="form-label">Area</label>
                                <select name="Survey.Area" id="Survey_Area" class="form-select" required>
                                    <option value="">Select Area</option>
                                    <option value="Front">Front</option>
                                    <option value="Drive">Drive</option>
                                    <option value="Kitchen">Kitchen</option>
                                    <option value="Hospitality">Hospitality</option>
                                </select>
                                <span asp-validation-for="Survey.Area" class="text-danger"></span>
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label asp-for="Survey.LeaderId" class="form-label">Leader Being Surveyed</label>
                                <!-- Add the area filter buttons container -->
                                <div class="area-filter-buttons mb-2">
                                    <button type="button" class="area-filter active" data-area="">All</button>
                                    <button type="button" class="area-filter" data-area="Front">
                                        <span class="area-badge area-front">Front</span>
                                    </button>
                                    <button type="button" class="area-filter" data-area="Drive">
                                        <span class="area-badge area-drive">Drive</span>
                                    </button>
                                    <button type="button" class="area-filter" data-area="Kitchen">
                                        <span class="area-badge area-kitchen">Kitchen</span>
                                    </button>
                                    <button type="button" class="area-filter" data-area="Hospitality">
                                        <span class="area-badge area-hospitality">Hospitality</span>
                                    </button>
                                </div>
                                <select name="Survey.LeaderId" id="Survey_LeaderId" class="form-select" required>
                                    <option value="">Select Leader Being Surveyed</option>
                                    @foreach (var leader in Model.Leaders)
                                    {
                                        <option value="@leader.Id" data-area="@leader.Area">@leader.Name</option>
                                    }
                                </select>
                                <span asp-validation-for="Survey.LeaderId" class="text-danger"></span>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label asp-for="Survey.EvaluatorLeaderId" class="form-label">Leader Taking Survey</label>
                                <!-- Add the area filter buttons container for evaluator -->
                                <div class="area-filter-buttons mb-2" id="evaluator-area-filters">
                                    <button type="button" class="evaluator-area-filter active" data-area="">All</button>
                                    <button type="button" class="evaluator-area-filter" data-area="Front">
                                        <span class="area-badge area-front">Front</span>
                                    </button>
                                    <button type="button" class="evaluator-area-filter" data-area="Drive">
                                        <span class="area-badge area-drive">Drive</span>
                                    </button>
                                    <button type="button" class="evaluator-area-filter" data-area="Kitchen">
                                        <span class="area-badge area-kitchen">Kitchen</span>
                                    </button>
                                    <button type="button" class="evaluator-area-filter" data-area="Hospitality">
                                        <span class="area-badge area-hospitality">Hospitality</span>
                                    </button>
                                </div>
                                <select name="Survey.EvaluatorLeaderId" id="Survey_EvaluatorLeaderId" class="form-select" required>
                                    <option value="">Select Leader Taking Survey</option>
                                    @foreach (var leader in Model.Leaders)
                                    {
                                        <option value="@leader.Id" data-area="@leader.Area">@leader.Name</option>
                                    }
                                </select>
                                <span asp-validation-for="Survey.EvaluatorLeaderId" class="text-danger"></span>
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label asp-for="Survey.MonthYear" class="form-label">Month/Year</label>
                                <input type="month" name="Survey.MonthYear" id="Survey_MonthYear" class="form-control" required />
                                <span asp-validation-for="Survey.MonthYear" class="text-danger"></span>
                            </div>
                        </div>
                    </div>
                    <div class="mb-3">
                        <label asp-for="Survey.Description" class="form-label">Description</label>
                        <textarea name="Survey.Description" id="Survey_Description" class="form-control" rows="3"></textarea>
                        <span asp-validation-for="Survey.Description" class="text-danger"></span>
                    </div>
                </div>
            </div>
            <!-- Questions Section -->
            <div class="card mb-4">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="mb-0">
                        Questions <span id="question-counter" class="badge bg-primary">0/10</span>
                    </h5>
                    <div>
                        <button type="button" class="btn btn-outline-primary btn-sm me-2" onclick="previewSurvey()" disabled title="Coming soon">
                            <i class="bi bi-plus-circle"></i> Preview Survey
                        </button>
                        <button type="button" class="btn btn-primary btn-sm" onclick="addNewQuestionRow(event)" data-role="add-question">
                            <i class="bi bi-plus-circle"></i> Add Question
                        </button>
                    </div>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table id="questionsTable" class="table table-cfa">
                            <thead>
                                <tr>
                                    <th style="width: 60px">#</th>
                                    <th>Question</th>
                                    <th style="width: 120px">Type</th>
                                    <th style="width: 100px">Actions</th>
                                    <th style="width: 40px"></th>
                                </tr>
                            </thead>
                            <tbody id="questionsContainer">
                                <!-- Questions will be added here dynamically -->
                                <!-- Optionally, you can include an example row or leave it empty -->
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>

            <!-- Form Actions -->
            <div class="d-flex justify-content-end gap-2">
                <a href="/Surveys" class="btn btn-secondary">Cancel</a>
                <button type="submit" class="btn btn-primary">Save Survey</button>
            </div>
        </form>
    </div>
</div>

<!-- Question Modal (if you want to use a modal for adding/editing questions) -->
<div class="modal fade" id="questionModal" tabindex="-1" aria-labelledby="questionModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="questionModalLabel">Add New Question</h5>
                <button type="button" class="btn btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <form id="questionForm" class="needs-validation" novalidate>
                    <input type="hidden" id="questionIndex" value="" />
                    <div class="mb-3">
                        <label for="questionText" class="form-label">Question Text</label>
                        <input type="text" class="form-control" id="questionText" required>
                        <div class="invalid-feedback">Please enter the question text.</div>
                    </div>
                    <div class="mb-3">
                        <label for="questionType" class="form-label">Question Type</label>
                        <select class="form-select" id="questionType" required>
                            <option value="yesno" selected>Yes/No</option>
                            <option value="score">Score (0-10)</option>
                        </select>
                        <div class="invalid-feedback">Please select a question type.</div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <button type="button" class="btn btn-primary" onclick="saveQuestion()">Save Question</button>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    <partial name="_ValidationScriptsPartial" />
    <script src="https://cdn.jsdelivr.net/npm/sortablejs@1.15.0/Sortable.min.js"></script>
    <script>
        // -------------------------------
        // QUESTION FUNCTIONALITY
        // -------------------------------
        // Global array to track questions
        let questions = [];

        // Global array to track categories
        let categories = [];
        @foreach (var category in Model.Categories)
        {
            <text>categories.push({ id: @category.Id, name: @Html.Raw(Json.Serialize(category.Name)) });</text>
        }

        function getAddQuestionButton() {
            return document.querySelector('[data-role="add-question"]');
        }

        window.addNewQuestionRow = function(event) {
            if (event) event.preventDefault();

            if (questions.length >= 10) {
                alert('Maximum of 10 questions allowed');
                return;
            }

            const tbody = document.getElementById('questionsContainer');
            const tr = document.createElement('tr');
            tr.dataset.isNew = 'true';
            tr.dataset.index = questions.length.toString();

            tr.innerHTML = `
                <td>${questions.length + 1}</td>
                <td>
                    <input type="text" class="form-control" placeholder="Enter question text" required>
                    <div class="mt-2">
                        <label class="form-label">Categories:</label>
                        <div class="category-selection">
                            ${categories.map(category => `
                                <div class="form-check form-check-inline category-checkbox category-${category.name.toLowerCase()}">
                                    <input class="form-check-input" type="checkbox" value="${category.id}" id="category-${category.id}-${questions.length}">
                                    <label class="form-check-label" for="category-${category.id}-${questions.length}">${category.name}</label>
                                </div>
                            `).join('')}
                        </div>
                    </div>
                </td>
                <td>
                    <select class="form-select">
                        <option value="yesno">Yes/No</option>
                        <option value="score">Score (0-10)</option>
                    </select>
                </td>
                <td>
                    <button type="button" class="btn btn-sm btn-success me-2" onclick="saveQuestionRow(this)">
                        <i class="bi bi-check-lg"></i>
                    </button>
                    <button type="button" class="btn btn-sm btn-danger" onclick="removeQuestionRow(this)">
                        <i class="bi bi-x-lg"></i>
                    </button>
                </td>
                <td>
                    <i class="bi bi-grip-vertical drag-handle"></i>
                </td>
            `;

            tbody.appendChild(tr);
            tr.querySelector('input').focus({preventScroll: true});

            // Disable the Add Question button while editing
            const addButton = getAddQuestionButton();
            if (addButton) addButton.disabled = true;

            updateQuestionCounter();
        };

        window.saveQuestionRow = function(button) {
            const row = button.closest('tr');
            const questionText = row.querySelector('input[type="text"]').value.trim();
            const questionType = row.querySelector('select').value;
            const index = parseInt(row.dataset.index);

            // Get selected categories
            const categoryCheckboxes = row.querySelectorAll('input[type="checkbox"]:checked');
            const categoryIds = Array.from(categoryCheckboxes).map(cb => parseInt(cb.value));

            if (!questionText) {
                alert('Question text is required');
                return;
            }

            if (row.dataset.isNew === 'true') {
                questions.push({
                    text: questionText,
                    type: questionType,
                    categoryIds: categoryIds
                });
                row.dataset.isNew = 'false';
            } else {
                questions[index] = {
                    text: questionText,
                    type: questionType,
                    categoryIds: categoryIds
                };
            }

            // Create category badges for display
            const categoryBadges = categoryIds.length > 0
                ? `<div class="mt-1">${categoryIds.map(id => {
                    const category = categories.find(c => c.id === id);
                    if (category) {
                        return `<span class="badge category-badge category-${category.name.toLowerCase()}">${category.name}</span>`;
                    }
                    return '';
                  }).join(' ')}</div>`
                : '';

            row.innerHTML = `
                <td class="question-number">${index + 1}</td>
                <td>
                    ${questionText}
                    ${categoryBadges}
                </td>
                <td>${questionType === 'yesno' ? 'Yes/No' : 'Score (0-10)'}</td>
                <td>
                    <div class="btn-group">
                        <button type="button" class="btn btn-sm btn-outline-primary" onclick="editQuestionRow(this)">
                            <i class="bi bi-pencil"></i>
                        </button>
                        <button type="button" class="btn btn-sm btn-outline-danger" onclick="removeQuestionRow(this)">
                            <i class="bi bi-trash"></i>
                        </button>
                    </div>
                </td>
                <td>
                    <i class="bi bi-grip-vertical drag-handle"></i>
                </td>
            `;

            // Re-enable the Add Question button after saving
            const addButton = getAddQuestionButton();
            if (addButton) addButton.disabled = false;

            updateQuestionCounter();
            updateQuestionsInput();
        };

        window.removeQuestionRow = function(button) {
            const row = button.closest('tr');
            const index = parseInt(row.dataset.index);

            if (confirm('Are you sure you want to delete this question?')) {
                // Remove from questions array if it's a saved question
                if (row.dataset.isNew !== 'true') {
                    questions.splice(index, 1);
                }

                // Remove the row
                row.remove();

                // Update indices for remaining rows
                document.querySelectorAll('#questionsContainer tr').forEach((row, idx) => {
                    row.dataset.index = idx.toString();
                    row.querySelector('td:first-child').textContent = (idx + 1).toString();
                });

                // Update counter and form inputs
                updateQuestionCounter();
                updateQuestionsInput();

                // Re-enable add button if needed
                const addButton = getAddQuestionButton();
                if (addButton) addButton.disabled = false;
            }
        };

        window.editQuestionRow = function(button) {
            const row = button.closest('tr');
            const index = parseInt(row.dataset.index);
            const question = questions[index];

            row.innerHTML = `
                <td>${row.cells[0].textContent}</td>
                <td>
                    <input type="text" class="form-control" value="${question.text}" required>
                    <div class="mt-2">
                        <label class="form-label">Categories:</label>
                        <div class="category-selection">
                            ${categories.map(category => `
                                <div class="form-check form-check-inline category-checkbox category-${category.name.toLowerCase()}">
                                    <input class="form-check-input" type="checkbox" value="${category.id}" id="category-${category.id}-${index}" ${question.categoryIds && question.categoryIds.includes(category.id) ? 'checked' : ''}>
                                    <label class="form-check-label" for="category-${category.id}-${index}">${category.name}</label>
                                </div>
                            `).join('')}
                        </div>
                    </div>
                </td>
                <td>
                    <select class="form-select">
                        <option value="yesno" ${question.type === 'yesno' ? 'selected' : ''}>Yes/No</option>
                        <option value="score" ${question.type === 'score' ? 'selected' : ''}>Score (0-10)</option>
                    </select>
                </td>
                <td>
                    <button type="button" class="btn btn-sm btn-success me-2" onclick="saveQuestionRow(this)">
                        <i class="bi bi-check-lg"></i>
                    </button>
                    <button type="button" class="btn btn-sm btn-danger" onclick="removeQuestionRow(this)">
                        <i class="bi bi-x-lg"></i>
                    </button>
                </td>
                <td>
                    <i class="bi bi-grip-vertical drag-handle"></i>
                </td>
            `;

            row.querySelector('input').focus();

            // Disable the Add Question button while editing
            const addButton = getAddQuestionButton();
            if (addButton) addButton.disabled = true;
        };

        function updateQuestionCounter() {
            const counter = document.getElementById('question-counter');
            const currentCount = questions.length;
            const hasNewQuestion = document.querySelector('#questionsContainer tr[data-is-new="true"]') !== null;

            counter.textContent = `${currentCount}/10`;

            const addButton = getAddQuestionButton();
            if (addButton) {
                addButton.disabled = currentCount >= 10 || hasNewQuestion;
            }
        }

        function updateQuestionsInput() {
            const form = document.getElementById('surveyForm');

            // Remove any existing question inputs
            form.querySelectorAll('input[name^="Survey.Questions"]').forEach(el => el.remove());

            // Add new hidden inputs for each question
            questions.forEach((question, index) => {
                const textInput = document.createElement('input');
                textInput.type = 'hidden';
                textInput.name = `Survey.Questions[${index}].Text`;
                textInput.value = question.text;
                form.appendChild(textInput);

                const typeInput = document.createElement('input');
                typeInput.type = 'hidden';
                typeInput.name = `Survey.Questions[${index}].QuestionType`;
                typeInput.value = question.type;
                form.appendChild(typeInput);

                // Add category IDs
                if (question.categoryIds && question.categoryIds.length > 0) {
                    question.categoryIds.forEach((categoryId, catIndex) => {
                        const categoryInput = document.createElement('input');
                        categoryInput.type = 'hidden';
                        categoryInput.name = `Survey.Questions[${index}].CategoryIds[${catIndex}]`;
                        categoryInput.value = categoryId;
                        form.appendChild(categoryInput);
                    });
                }
            });
        }

        // -------------------------------
        // AREA FILTER & LEADER SELECTION
        // -------------------------------
        document.addEventListener('DOMContentLoaded', function() {
            const areaSelect = document.getElementById('Survey_Area');
            const leaderSelect = document.getElementById('Survey_LeaderId');
            const evaluatorLeaderSelect = document.getElementById('Survey_EvaluatorLeaderId');
            const areaFilterButtons = document.querySelectorAll('.area-filter');
            const evaluatorAreaFilterButtons = document.querySelectorAll('.evaluator-area-filter');

            // Store original leaders for filtering
            const originalOptions = Array.from(leaderSelect.options)
                .filter(option => option.value !== "")
                .sort((a, b) => a.text.localeCompare(b.text));

            // Store original evaluator leaders for filtering
            const originalEvaluatorOptions = Array.from(evaluatorLeaderSelect.options)
                .filter(option => option.value !== "")
                .sort((a, b) => a.text.localeCompare(b.text));

            function filterLeadersByArea(selectElement, originalOpts, selectedArea) {
                // Keep the placeholder
                const placeholder = selectElement.options[0];

                // Clear current options except placeholder
                selectElement.innerHTML = '';
                selectElement.appendChild(placeholder);

                // Filter and add leaders
                originalOpts.forEach(option => {
                    if (!selectedArea || option.dataset.area === selectedArea) {
                        selectElement.appendChild(option.cloneNode(true));
                    }
                });
            }

            // Handle area filter button clicks for leader being surveyed
            areaFilterButtons.forEach(button => {
                button.addEventListener('click', function() {
                    // Remove active class from all buttons
                    areaFilterButtons.forEach(btn => btn.classList.remove('active'));
                    // Add active class to clicked button
                    this.classList.add('active');

                    const selectedArea = this.dataset.area;
                    filterLeadersByArea(leaderSelect, originalOptions, selectedArea);
                });
            });

            // Handle area filter button clicks for evaluator leader
            evaluatorAreaFilterButtons.forEach(button => {
                button.addEventListener('click', function() {
                    // Remove active class from all buttons
                    evaluatorAreaFilterButtons.forEach(btn => btn.classList.remove('active'));
                    // Add active class to clicked button
                    this.classList.add('active');

                    const selectedArea = this.dataset.area;
                    filterLeadersByArea(evaluatorLeaderSelect, originalEvaluatorOptions, selectedArea);
                });
            });

            // Handle leader select changes
            if (leaderSelect) {
                leaderSelect.addEventListener('change', function() {
                    const selectedOption = this.options[this.selectedIndex];
                    if (selectedOption.value && areaSelect && !areaSelect.value) {
                        const leaderArea = selectedOption.dataset.area;
                        areaSelect.value = leaderArea;
                    }
                });
            }

            // Handle evaluator leader select changes
            if (evaluatorLeaderSelect) {
                evaluatorLeaderSelect.addEventListener('change', function() {
                    // You can add specific logic for evaluator leader selection if needed
                });
            }

            // Handle area select changes
            if (areaSelect) {
                areaSelect.addEventListener('change', function() {
                    // Do not update filter buttons when area select changes
                    // Only update the leader list if needed based on selection
                });
            }
        });

        document.getElementById('surveyForm').addEventListener('submit', async function(event) {
            event.preventDefault();

            // Debug: Check if elements exist and their values
            console.log('Debugging form elements:');
            console.log('Name field:', document.querySelector('[name="Survey.Name"]')?.value);
            console.log('Area field:', document.querySelector('[name="Survey.Area"]')?.value);
            console.log('MonthYear field:', document.querySelector('[name="Survey.MonthYear"]')?.value);
            console.log('LeaderId field:', document.querySelector('[name="Survey.LeaderId"]')?.value);
            console.log('EvaluatorLeaderId field:', document.querySelector('[name="Survey.EvaluatorLeaderId"]')?.value);

            // Create FormData and manually append fields
            const formData = new FormData();

            // Add antiforgery token
            const token = document.querySelector('[name="__RequestVerificationToken"]').value;
            formData.append('__RequestVerificationToken', token);

            // Add survey fields - with null checks
            const nameField = document.querySelector('[name="Survey.Name"]');
            const areaField = document.querySelector('[name="Survey.Area"]');
            const monthYearField = document.querySelector('[name="Survey.MonthYear"]');
            const leaderIdField = document.querySelector('[name="Survey.LeaderId"]');
            const evaluatorLeaderIdField = document.querySelector('[name="Survey.EvaluatorLeaderId"]');
            const descriptionField = document.querySelector('[name="Survey.Description"]');

            if (!nameField || !areaField || !monthYearField || !leaderIdField || !evaluatorLeaderIdField) {
                console.error('Required form fields are missing:');
                console.error('Name field exists:', !!nameField);
                console.error('Area field exists:', !!areaField);
                console.error('MonthYear field exists:', !!monthYearField);
                console.error('LeaderId field exists:', !!leaderIdField);
                console.error('EvaluatorLeaderId field exists:', !!evaluatorLeaderIdField);
                alert('Form is missing required fields. Check console for details.');
                return;
            }

            formData.append('Survey.Name', nameField.value);
            formData.append('Survey.Area', areaField.value);
            formData.append('Survey.MonthYear', monthYearField.value);
            formData.append('Survey.LeaderId', leaderIdField.value);
            formData.append('Survey.EvaluatorLeaderId', evaluatorLeaderIdField.value);
            formData.append('Survey.Description', descriptionField?.value || '');

            // Add questions
            questions.forEach((question, index) => {
                formData.append(`Survey.Questions[${index}].Text`, question.text);
                formData.append(`Survey.Questions[${index}].QuestionType`, question.type);
            });

            // Log all form data before submission
            console.log('Final form data being submitted:');
            for (let [key, value] of formData.entries()) {
                console.log(`${key}: ${value}`);
            }

            try {
                const response = await fetch('/NewSurvey', {
                    method: 'POST',
                    headers: {
                        'RequestVerificationToken': token,
                        'X-Requested-With': 'XMLHttpRequest'
                    },
                    body: formData
                });

                if (!response.ok) {
                    const contentType = response.headers.get("content-type");
                    const responseData = contentType && contentType.includes("application/json")
                        ? await response.json()
                        : await response.text();

                    console.error('Response status:', response.status);
                    console.error('Response data:', responseData);

                    if (responseData.errors) {
                        console.error('Validation errors:');
                        Object.entries(responseData.errors).forEach(([field, errors]) => {
                            // Find the corresponding input field
                            const inputField = document.querySelector(`[name="${field}"]`);
                            if (inputField) {
                                inputField.classList.add('is-invalid');
                                // Find or create validation message element
                                let validationMessage = inputField.parentElement.querySelector('.invalid-feedback');
                                if (!validationMessage) {
                                    validationMessage = document.createElement('div');
                                    validationMessage.className = 'invalid-feedback';
                                    inputField.parentElement.appendChild(validationMessage);
                                }
                                validationMessage.textContent = errors.join(', ');
                            }
                            console.error(`${field}:`, errors);
                        });
                        alert('Please check the form for errors and try again.');
                    } else {
                        alert('Server error occurred. Please try again.');
                    }
                    return;
                }

                const responseData = await response.json();
                if (responseData.success) {
                    window.location.href = responseData.redirectTo || '/Surveys';
                } else {
                    alert('Failed to save survey');
                }
            } catch (error) {
                console.error('Error submitting form:', error);
                alert('Error submitting form. Check console for details.');
            }
        });
    </script>

    <!-- Link to external CSS files for survey editor styles -->
    <link rel="stylesheet" href="/css/survey-editor.css" />
    <link rel="stylesheet" href="/css/survey-categories.css" />
}
